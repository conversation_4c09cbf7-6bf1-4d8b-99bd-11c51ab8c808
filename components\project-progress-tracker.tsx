"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CheckCircle,
  Clock,
  Plus,
  Edit,
  Trash2,
  Target,
  BarChart3,
  Calendar,
} from "lucide-react";

interface ProjectStep {
  id: string;
  title: string;
  description: string;
  status: "not-started" | "in-progress" | "completed" | "blocked";
  priority: "low" | "medium" | "high";
  estimatedHours: number;
  actualHours?: number;
  startDate?: string;
  endDate?: string;
  notes?: string;
}

interface ProjectProgressTrackerProps {
  projectId: string;
  projectTitle: string;
}

export default function ProjectProgressTracker({
  projectId,
  projectTitle,
}: ProjectProgressTrackerProps) {
  const [steps, setSteps] = useState<ProjectStep[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingStep, setEditingStep] = useState<ProjectStep | null>(null);
  const [newStep, setNewStep] = useState<Partial<ProjectStep>>({
    title: "",
    description: "",
    status: "not-started",
    priority: "medium",
    estimatedHours: 1,
  });

  // Charger les étapes depuis le localStorage
  useEffect(() => {
    const savedSteps = localStorage.getItem(`project-steps-${projectId}`);
    if (savedSteps) {
      setSteps(JSON.parse(savedSteps));
    } else {
      // Étapes par défaut
      const defaultSteps: ProjectStep[] = [
        {
          id: "1",
          title: "Configuration de l'environnement",
          description:
            "Installer les dépendances et configurer l'environnement de développement",
          status: "not-started",
          priority: "high",
          estimatedHours: 2,
        },
        {
          id: "2",
          title: "Analyse des exigences",
          description:
            "Comprendre les objectifs et les fonctionnalités à implémenter",
          status: "not-started",
          priority: "high",
          estimatedHours: 3,
        },
        {
          id: "3",
          title: "Conception de l'architecture",
          description: "Planifier la structure du code et les composants",
          status: "not-started",
          priority: "medium",
          estimatedHours: 4,
        },
        {
          id: "4",
          title: "Développement des fonctionnalités principales",
          description: "Implémenter les fonctionnalités de base",
          status: "not-started",
          priority: "high",
          estimatedHours: 8,
        },
        {
          id: "5",
          title: "Tests et débogage",
          description: "Tester l'application et corriger les bugs",
          status: "not-started",
          priority: "medium",
          estimatedHours: 3,
        },
        {
          id: "6",
          title: "Documentation",
          description: "Rédiger la documentation du projet",
          status: "not-started",
          priority: "low",
          estimatedHours: 2,
        },
      ];
      setSteps(defaultSteps);
      localStorage.setItem(
        `project-steps-${projectId}`,
        JSON.stringify(defaultSteps)
      );
    }
  }, [projectId]);

  // Sauvegarder les étapes dans le localStorage
  const saveSteps = (updatedSteps: ProjectStep[]) => {
    setSteps(updatedSteps);
    localStorage.setItem(
      `project-steps-${projectId}`,
      JSON.stringify(updatedSteps)
    );
  };

  const addStep = () => {
    if (!newStep.title || !newStep.description) return;

    const step: ProjectStep = {
      id: Date.now().toString(),
      title: newStep.title,
      description: newStep.description,
      status: newStep.status as ProjectStep["status"],
      priority: newStep.priority as ProjectStep["priority"],
      estimatedHours: newStep.estimatedHours || 1,
    };

    saveSteps([...steps, step]);
    setNewStep({
      title: "",
      description: "",
      status: "not-started",
      priority: "medium",
      estimatedHours: 1,
    });
    setIsAddDialogOpen(false);
  };

  const updateStep = (stepId: string, updates: Partial<ProjectStep>) => {
    const updatedSteps = steps.map((step) => {
      if (step.id === stepId) {
        const updatedStep = { ...step, ...updates };

        // Automatiquement définir les dates de début et fin
        if (updates.status === "in-progress" && !step.startDate) {
          updatedStep.startDate = new Date().toISOString().split("T")[0];
        }
        if (updates.status === "completed" && !step.endDate) {
          updatedStep.endDate = new Date().toISOString().split("T")[0];
        }

        return updatedStep;
      }
      return step;
    });
    saveSteps(updatedSteps);
  };

  const deleteStep = (stepId: string) => {
    const updatedSteps = steps.filter((step) => step.id !== stepId);
    saveSteps(updatedSteps);
  };

  const getPriorityColor = (priority: ProjectStep["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: ProjectStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "blocked":
        return <Target className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const completedSteps = steps.filter(
    (step) => step.status === "completed"
  ).length;
  const inProgressSteps = steps.filter(
    (step) => step.status === "in-progress"
  ).length;
  const totalSteps = steps.length;
  const progressPercentage =
    totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  const totalEstimatedHours = steps.reduce(
    (sum, step) => sum + step.estimatedHours,
    0
  );
  const completedHours = steps
    .filter((step) => step.status === "completed")
    .reduce((sum, step) => sum + step.estimatedHours, 0);

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Suivi des étapes du projet
            </CardTitle>
            <CardDescription>
              Gérez et suivez votre progression sur {projectTitle}
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Ajouter une étape
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Ajouter une nouvelle étape</DialogTitle>
                <DialogDescription>
                  Créez une nouvelle étape pour votre projet
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Titre</Label>
                  <Input
                    id="title"
                    value={newStep.title}
                    onChange={(e) =>
                      setNewStep({ ...newStep, title: e.target.value })
                    }
                    placeholder="Titre de l'étape"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newStep.description}
                    onChange={(e) =>
                      setNewStep({ ...newStep, description: e.target.value })
                    }
                    placeholder="Description détaillée"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="priority">Priorité</Label>
                    <Select
                      value={newStep.priority}
                      onValueChange={(value) =>
                        setNewStep({
                          ...newStep,
                          priority: value as ProjectStep["priority"],
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Basse</SelectItem>
                        <SelectItem value="medium">Moyenne</SelectItem>
                        <SelectItem value="high">Haute</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="estimatedHours">Heures estimées</Label>
                    <Input
                      id="estimatedHours"
                      type="number"
                      min="0.5"
                      step="0.5"
                      value={newStep.estimatedHours}
                      onChange={(e) =>
                        setNewStep({
                          ...newStep,
                          estimatedHours: parseFloat(e.target.value),
                        })
                      }
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Annuler
                </Button>
                <Button onClick={addStep}>Ajouter</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Progression</p>
                  <p className="text-2xl font-bold">
                    {progressPercentage.toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Terminées</p>
                  <p className="text-2xl font-bold">
                    {completedSteps}/{totalSteps}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">En cours</p>
                  <p className="text-2xl font-bold">{inProgressSteps}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm font-medium">Heures</p>
                  <p className="text-2xl font-bold">
                    {completedHours}/{totalEstimatedHours}h
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Barre de progression */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Progression globale</span>
            <span className="text-sm text-gray-600">
              {completedSteps}/{totalSteps} étapes terminées
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Tableau des étapes */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Étape</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Priorité</TableHead>
              <TableHead>Temps estimé</TableHead>
              <TableHead>Dates</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {steps.map((step) => (
              <TableRow key={step.id}>
                <TableCell>
                  <div className="flex items-start gap-3">
                    {getStatusIcon(step.status)}
                    <div>
                      <div className="font-medium">{step.title}</div>
                      <div className="text-sm text-gray-600">
                        {step.description}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Select
                    value={step.status}
                    onValueChange={(value) =>
                      updateStep(step.id, {
                        status: value as ProjectStep["status"],
                      })
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="not-started">Non commencé</SelectItem>
                      <SelectItem value="in-progress">En cours</SelectItem>
                      <SelectItem value="completed">Terminé</SelectItem>
                      <SelectItem value="blocked">Bloqué</SelectItem>
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>
                  <Badge className={getPriorityColor(step.priority)}>
                    {step.priority === "high"
                      ? "Haute"
                      : step.priority === "medium"
                      ? "Moyenne"
                      : "Basse"}
                  </Badge>
                </TableCell>
                <TableCell>{step.estimatedHours}h</TableCell>
                <TableCell>
                  <div className="text-sm">
                    {step.startDate && (
                      <div>
                        Début: {new Date(step.startDate).toLocaleDateString()}
                      </div>
                    )}
                    {step.endDate && (
                      <div>
                        Fin: {new Date(step.endDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingStep(step)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteStep(step.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Dialog d'édition */}
        {editingStep && (
          <Dialog
            open={!!editingStep}
            onOpenChange={() => setEditingStep(null)}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Modifier l'étape</DialogTitle>
                <DialogDescription>
                  Modifiez les détails de cette étape
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-title">Titre</Label>
                  <Input
                    id="edit-title"
                    value={editingStep.title}
                    onChange={(e) =>
                      setEditingStep({ ...editingStep, title: e.target.value })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={editingStep.description}
                    onChange={(e) =>
                      setEditingStep({
                        ...editingStep,
                        description: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-actual-hours">Heures réelles</Label>
                    <Input
                      id="edit-actual-hours"
                      type="number"
                      min="0"
                      step="0.5"
                      value={editingStep.actualHours || ""}
                      onChange={(e) =>
                        setEditingStep({
                          ...editingStep,
                          actualHours: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                      placeholder="Heures travaillées"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-estimated-hours">
                      Heures estimées
                    </Label>
                    <Input
                      id="edit-estimated-hours"
                      type="number"
                      min="0.5"
                      step="0.5"
                      value={editingStep.estimatedHours}
                      onChange={(e) =>
                        setEditingStep({
                          ...editingStep,
                          estimatedHours: parseFloat(e.target.value),
                        })
                      }
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit-notes">Notes</Label>
                  <Textarea
                    id="edit-notes"
                    value={editingStep.notes || ""}
                    onChange={(e) =>
                      setEditingStep({ ...editingStep, notes: e.target.value })
                    }
                    placeholder="Notes personnelles sur cette étape..."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setEditingStep(null)}>
                  Annuler
                </Button>
                <Button
                  onClick={() => {
                    updateStep(editingStep.id, editingStep);
                    setEditingStep(null);
                  }}
                >
                  Sauvegarder
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  );
}
