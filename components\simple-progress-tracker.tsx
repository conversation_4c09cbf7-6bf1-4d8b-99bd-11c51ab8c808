"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CheckCircle,
  Clock,
  Plus,
  Edit,
  Target,
  BarChart3,
} from "lucide-react";

// Types
interface Step {
  id: string;
  title: string;
  status: "todo" | "doing" | "done";
  priority: "low" | "medium" | "high";
  hours: number;
  startDate?: string;
  endDate?: string;
}

interface Props {
  projectId: string;
  projectTitle: string;
}

// Étapes par défaut
const DEFAULT_STEPS: Step[] = [
  {
    id: "1",
    title: "Configuration environnement",
    status: "todo",
    priority: "high",
    hours: 2,
  },
  {
    id: "2",
    title: "Analyse des exigences",
    status: "todo",
    priority: "high",
    hours: 3,
  },
  {
    id: "3",
    title: "Conception architecture",
    status: "todo",
    priority: "medium",
    hours: 4,
  },
  {
    id: "4",
    title: "Développement principal",
    status: "todo",
    priority: "high",
    hours: 8,
  },
  {
    id: "5",
    title: "Tests et débogage",
    status: "todo",
    priority: "medium",
    hours: 3,
  },
  {
    id: "6",
    title: "Documentation",
    status: "todo",
    priority: "low",
    hours: 2,
  },
];

export default function SimpleProgressTracker({
  projectId,
  projectTitle,
}: Props) {
  const [steps, setSteps] = useState<Step[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newStep, setNewStep] = useState({
    title: "",
    hours: 1,
    priority: "medium" as Step["priority"],
  });

  // Charger les données
  useEffect(() => {
    const saved = localStorage.getItem(`steps-${projectId}`);
    setSteps(saved ? JSON.parse(saved) : DEFAULT_STEPS);
  }, [projectId]);

  // Sauvegarder les données
  const saveSteps = (newSteps: Step[]) => {
    setSteps(newSteps);
    localStorage.setItem(`steps-${projectId}`, JSON.stringify(newSteps));
  };

  // Mettre à jour une étape
  const updateStep = (stepId: string, updates: Partial<Step>) => {
    const updatedSteps = steps.map((step) => {
      if (step.id === stepId) {
        const updated = { ...step, ...updates };

        // Gérer les dates automatiquement
        if (updates.status === "doing" && !step.startDate) {
          updated.startDate = new Date().toISOString().split("T")[0];
        }
        if (updates.status === "done" && !step.endDate) {
          updated.endDate = new Date().toISOString().split("T")[0];
        }

        return updated;
      }
      return step;
    });
    saveSteps(updatedSteps);
  };

  // Ajouter une étape
  const addStep = () => {
    if (!newStep.title.trim()) return;

    const step: Step = {
      id: Date.now().toString(),
      title: newStep.title,
      status: "todo",
      priority: newStep.priority,
      hours: newStep.hours,
    };

    saveSteps([...steps, step]);
    setNewStep({ title: "", hours: 1, priority: "medium" });
    setShowAddDialog(false);
  };

  // Supprimer une étape
  const deleteStep = (stepId: string) => {
    saveSteps(steps.filter((step) => step.id !== stepId));
  };

  // Calculer les statistiques
  const stats = {
    total: steps.length,
    done: steps.filter((s) => s.status === "done").length,
    doing: steps.filter((s) => s.status === "doing").length,
    totalHours: steps.reduce((sum, s) => sum + s.hours, 0),
    doneHours: steps
      .filter((s) => s.status === "done")
      .reduce((sum, s) => sum + s.hours, 0),
  };

  const progress = stats.total > 0 ? (stats.done / stats.total) * 100 : 0;

  // Fonctions utilitaires pour les couleurs
  const getStatusIcon = (status: Step["status"]) => {
    switch (status) {
      case "done":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "doing":
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: Step["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
    }
  };

  const getStatusText = (status: Step["status"]) => {
    switch (status) {
      case "todo":
        return "À faire";
      case "doing":
        return "En cours";
      case "done":
        return "Terminé";
    }
  };

  const getPriorityText = (priority: Step["priority"]) => {
    switch (priority) {
      case "high":
        return "Haute";
      case "medium":
        return "Moyenne";
      case "low":
        return "Basse";
    }
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Suivi des étapes - {projectTitle}
            </CardTitle>
          </div>

          {/* Bouton Ajouter */}
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Ajouter
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Nouvelle étape</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Titre</Label>
                  <Input
                    value={newStep.title}
                    onChange={(e) =>
                      setNewStep({ ...newStep, title: e.target.value })
                    }
                    placeholder="Nom de l'étape"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Priorité</Label>
                    <Select
                      value={newStep.priority}
                      onValueChange={(value) =>
                        setNewStep({
                          ...newStep,
                          priority: value as Step["priority"],
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Basse</SelectItem>
                        <SelectItem value="medium">Moyenne</SelectItem>
                        <SelectItem value="high">Haute</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Heures</Label>
                    <Input
                      type="number"
                      min="0.5"
                      step="0.5"
                      value={newStep.hours}
                      onChange={(e) =>
                        setNewStep({
                          ...newStep,
                          hours: parseFloat(e.target.value) || 1,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowAddDialog(false)}
                >
                  Annuler
                </Button>
                <Button onClick={addStep}>Ajouter</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Progression</p>
                  <p className="text-2xl font-bold">{progress.toFixed(0)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Terminées</p>
                  <p className="text-2xl font-bold">
                    {stats.done}/{stats.total}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">En cours</p>
                  <p className="text-2xl font-bold">{stats.doing}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm font-medium">Heures</p>
                  <p className="text-2xl font-bold">
                    {stats.doneHours}/{stats.totalHours}h
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Barre de progression */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Progression globale</span>
            <span className="text-sm text-gray-600">
              {stats.done}/{stats.total} étapes
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Tableau des étapes */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Étape</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Priorité</TableHead>
              <TableHead>Heures</TableHead>
              <TableHead>Dates</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {steps.map((step) => (
              <TableRow key={step.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    {getStatusIcon(step.status)}
                    <span className="font-medium">{step.title}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Select
                    value={step.status}
                    onValueChange={(value) =>
                      updateStep(step.id, { status: value as Step["status"] })
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todo">À faire</SelectItem>
                      <SelectItem value="doing">En cours</SelectItem>
                      <SelectItem value="done">Terminé</SelectItem>
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell>
                  <Badge className={getPriorityColor(step.priority)}>
                    {getPriorityText(step.priority)}
                  </Badge>
                </TableCell>
                <TableCell>{step.hours}h</TableCell>
                <TableCell>
                  <div className="text-sm text-gray-600">
                    {step.startDate && (
                      <div>
                        Début: {new Date(step.startDate).toLocaleDateString()}
                      </div>
                    )}
                    {step.endDate && (
                      <div>
                        Fin: {new Date(step.endDate).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteStep(step.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Supprimer
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
